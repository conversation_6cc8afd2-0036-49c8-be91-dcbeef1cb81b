// 全局样式重置
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
    line-height: 1.6;
    color: #2c3e50;
    scroll-behavior: smooth;
}

body {
    margin: 0;
    padding: 0;
    background-color: #ffffff;
    transition: background-color 0.3s ease;
}

// 暗色主题
html.dark {
    body {
        background-color: #1a1a1a;
        color: #e2e8f0;
    }
}

// 滚动条样式
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

// 暗色主题滚动条
html.dark {
     ::-webkit-scrollbar-track {
        background: #2d2d2d;
    }
     ::-webkit-scrollbar-thumb {
        background: #555;
    }
     ::-webkit-scrollbar-thumb:hover {
        background: #777;
    }
}