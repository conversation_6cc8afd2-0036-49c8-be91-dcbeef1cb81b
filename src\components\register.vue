<template>
  <div class="register-container">
    <h3 class="title">用户注册</h3>
    <el-form ref="registerForm_ref" :model="registerForm" :rules="registerRules" label-width="80px">
      <el-form-item label="用户名" prop="username">
        <el-input v-model="registerForm.username" placeholder="请输入用户名"></el-input>
      </el-form-item>
      <el-form-item label="密码" prop="password">
        <el-input v-model="registerForm.password" placeholder="请输入密码" type="password"></el-input>
      </el-form-item>
      <el-form-item label="确认密码" prop="confirm_password">
        <el-input v-model="registerForm.confirm_password" placeholder="请再次输入密码" type="password"></el-input>
      </el-form-item>
      <el-form-item style="text-align: center;">
        <el-button type="primary" @click="handleRegister">注册</el-button>
        <el-button @click="goToLogin">返回登录</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'

const router = useRouter()
const registerForm = reactive({
  username: '',
  password: '',
  confirm_password: ''
})

const validatePass = (rule: any, value: string, callback: any) => {
  if (value === '') {
    callback(new Error('请输入密码'))
  } else {
    if (registerForm.confirm_password !== '') {
      registerForm_ref.value?.validateField('confirm_password')
    }
    callback()
  }
}

const validateConfirmPass = (rule: any, value: string, callback: any) => {
  if (value === '') {
    callback(new Error('请再次输入密码'))
  } else if (value !== registerForm.password) {
    callback(new Error('两次输入密码不一致!'))
  } else {
    callback()
  }
}

const registerRules = {
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  password: [{ required: true, validator: validatePass, trigger: 'blur' }],
  confirm_password: [{ required: true, validator: validateConfirmPass, trigger: 'blur' }]
}

const registerForm_ref = ref<FormInstance>()

// 注册处理
const handleRegister = () => {
  if (!registerForm_ref.value) return
  
  registerForm_ref.value.validate((valid: boolean) => {
    if (valid) {
      ElMessage({
        message: '注册功能尚未实现，请返回登录页',
        type: 'warning'
      })
      // 这里可以添加注册API调用
    }
  })
}

// 返回登录页面
const goToLogin = () => {
  router.push('/login')
}
</script>

<style scoped>
.register-container {
  width: 400px;
  margin: 100px auto;
  padding: 30px;
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.title {
  text-align: center;
  margin-bottom: 30px;
  color: #333;
}
</style> 