<template>
    <div class="login-container">
      <h3 class="title">用户登录</h3>
      <el-form ref="loginForm_ref" :model="loginForm" :rules="loginRules" label-width="80px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="loginForm.username" placeholder="请输入用户名"></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="loginForm.password" placeholder="请输入密码" type="password"></el-input>
        </el-form-item>
        <el-form-item style="text-align: center;">
          <el-button type="primary" @click="handleLogin">登录</el-button>
          <el-button @click="goToRegister">注册账号</el-button>
        </el-form-item>
      </el-form>
    </div>
  </template>
  
<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ref, reactive } from 'vue'
import type { FormInstance } from 'element-plus'
import { useUserStore } from '@/store/user'

const router = useRouter()
const userStore = useUserStore()

const loginForm = reactive({
  username: '',
  password: ''
})

const loginRules = {
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }]
}

const loginForm_ref = ref<FormInstance>()

// 登录处理
const handleLogin = () => {
  if (!loginForm_ref.value) return
  
  loginForm_ref.value.validate(async (valid: boolean) => {
    if (valid) {
      const success = await userStore.login(loginForm)
      if (success) {
        router.push('/home')
      }
    }
  })
}

// const login = async (username, password) => {
//   try {
//     const response = await axios.post('/api/login', {
//       username: username,
//       password: password
//     });
//     console.log(response.data); // 处理响应数据
//   } catch (error) {
//     console.error(error); // 处理错误
//   }
// };


// 跳转到注册页面
const goToRegister = () => {
  router.push('/register')
}
</script>
  
<style scoped lang="scss">
.login-container {
  width: 350px;
  margin: 100px auto;
  padding: 30px;
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.title {
  text-align: center;
  margin-bottom: 30px;
  color: #333;
}
</style>