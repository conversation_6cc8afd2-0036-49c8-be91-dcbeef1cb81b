<template>
  <div class="home-page">
    <!-- 导航栏 -->
    <Navigation />

    <!-- 主要内容区域 -->
    <main class="main-content">
      <!-- Hero 区域 -->
      <section class="hero-section">
        <div class="hero-container">
          <h1 class="hero-title animate-fade-in">
            <span class="title-progressive">渐进式</span>
            <br>
            <span class="title-javascript">JavaScript</span>
            <span class="title-framework">框架</span>
          </h1>
          <p class="hero-description animate-fade-in-delay">
            易学易用，性能出色，适用场景丰富的 Web 前端框架。
          </p>
          <div class="hero-actions animate-fade-in-delay-2">
            <router-link to="/login" class="btn btn-primary">快速上手</router-link>
            <button class="btn btn-secondary" @click="scrollToFeatures">了解特性</button>
            <a href="https://vuejs.org" target="_blank" class="btn btn-outline">
              访问官方文档
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M7 17L17 7M17 7H7M17 7V17"/>
              </svg>
            </a>
          </div>
        </div>
      </section>

      <!-- 特性展示区域 -->
      <section id="features" class="features-section">
        <div class="features-container">
          <div class="feature-card">
            <div class="feature-icon">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/>
                <path d="M9 12l2 2 4-4"/>
              </svg>
            </div>
            <h3 class="feature-title">易学易用</h3>
            <p class="feature-description">
              基于标准 HTML、CSS 和 JavaScript 构建，提供容易上手的 API 和一流的文档。
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"/>
              </svg>
            </div>
            <h3 class="feature-title">性能出色</h3>
            <p class="feature-description">
              经过编译器优化、完全响应式的渲染系统，几乎不需要手动优化。
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-icon">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"/>
                <polyline points="3.27,6.96 12,12.01 20.73,6.96"/>
                <line x1="12" y1="22.08" x2="12" y2="12"/>
              </svg>
            </div>
            <h3 class="feature-title">灵活多样</h3>
            <p class="feature-description">
              丰富的、可渐进式集成的生态系统，可以在一个库和一套完整框架之间伸缩自如。
            </p>
          </div>
        </div>
      </section>

      <!-- 赞助商区域 -->
      <section id="sponsors" class="sponsors-section">
        <div class="sponsors-container">
          <div class="sponsor-tier">
            <h4 class="sponsor-title">中国区铂金赞助商</h4>
            <div class="sponsor-logos">
              <div class="sponsor-logo">
                <div class="logo-placeholder">赞助商 Logo</div>
              </div>
            </div>
          </div>
          <div class="sponsor-tier">
            <h4 class="sponsor-title">开源软件系统</h4>
            <div class="sponsor-logos">
              <div class="sponsor-logo">
                <div class="logo-placeholder">开源系统 Logo</div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- 页脚 -->
    <footer class="footer">
      <div class="footer-container">
        <div class="footer-content">
          <div class="footer-section">
            <h5 class="footer-title">资源</h5>
            <ul class="footer-links">
              <li><a href="#">合作伙伴</a></li>
              <li><a href="#">主题</a></li>
              <li><a href="#">工作</a></li>
              <li><a href="#">T-Shirt 商店</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h5 class="footer-title">帮助</h5>
            <ul class="footer-links">
              <li><a href="#">Discord 聊天室</a></li>
              <li><a href="#">GitHub 讨论版</a></li>
              <li><a href="#">DEV Community</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h5 class="footer-title">新闻</h5>
            <ul class="footer-links">
              <li><a href="#">博客</a></li>
              <li><a href="#">Twitter</a></li>
              <li><a href="#">活动</a></li>
              <li><a href="#">新闻简报</a></li>
            </ul>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2014-2024 Evan You</p>
          <p>中文文档内容版权归属 Vue.js 中文文档翻译团队。</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import Navigation from '@/components/Navigation.vue'

const scrollToFeatures = () => {
  const featuresSection = document.getElementById('features')
  if (featuresSection) {
    featuresSection.scrollIntoView({ behavior: 'smooth' })
  }
}
</script>

<style scoped lang="scss">
.home-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #2c3e50;
}

// 主要内容样式
.main-content {
  padding-top: 64px;
}

// 动画样式
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeInUp 0.8s ease-out;
}

.animate-fade-in-delay {
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.animate-fade-in-delay-2 {
  animation: fadeInUp 0.8s ease-out 0.4s both;
}

// Hero 区域样式
.hero-section {
  padding: 120px 0;
  text-align: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.hero-title {
  font-size: 4rem;
  font-weight: 700;
  margin-bottom: 24px;
  line-height: 1.2;
  
  .title-progressive {
    color: #4fc08d;
  }
  
  .title-javascript {
    color: #ffd700;
  }
  
  .title-framework {
    color: #ffffff;
  }
}

.hero-description {
  font-size: 1.25rem;
  margin-bottom: 48px;
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hero-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

.btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
  }

  &:hover::before {
    width: 300px;
    height: 300px;
  }

  &.btn-primary {
    background: linear-gradient(135deg, #42b883, #369870);
    color: white;
    box-shadow: 0 4px 15px rgba(66, 184, 131, 0.3);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(66, 184, 131, 0.4);
    }
  }

  &.btn-secondary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);

    &:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-2px);
      border-color: rgba(255, 255, 255, 0.5);
    }
  }

  &.btn-outline {
    background: transparent;
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.5);

    &:hover {
      background: rgba(255, 255, 255, 0.1);
      transform: translateY(-2px);
      border-color: rgba(255, 255, 255, 0.8);
    }
  }
}

// 特性区域样式
.features-section {
  padding: 120px 0;
  background: white;
}

.features-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 48px;
}

.feature-card {
  text-align: center;
  padding: 32px;
  border-radius: 16px;
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(66, 184, 131, 0.1), transparent);
    transition: left 0.5s ease;
  }

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border-color: rgba(66, 184, 131, 0.2);

    &::before {
      left: 100%;
    }
  }
}

.feature-icon {
  margin-bottom: 24px;
  color: #42b883;
}

.feature-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 16px;
  color: #2c3e50;
}

.feature-description {
  color: #666;
  line-height: 1.6;
}

// 赞助商区域样式
.sponsors-section {
  padding: 80px 0;
  background: #f8f9fa;
}

.sponsors-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  text-align: center;
}

.sponsor-tier {
  margin-bottom: 48px;
}

.sponsor-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 24px;
  color: #666;
}

.sponsor-logos {
  display: flex;
  justify-content: center;
  gap: 32px;
  flex-wrap: wrap;
}

.sponsor-logo {
  .logo-placeholder {
    width: 120px;
    height: 60px;
    background: #e9ecef;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    font-size: 12px;
  }
}

// 页脚样式
.footer {
  background: #2c3e50;
  color: white;
  padding: 64px 0 32px;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 48px;
  margin-bottom: 48px;
}

.footer-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 16px;
}

.footer-links {
  list-style: none;
  padding: 0;
  
  li {
    margin-bottom: 8px;
  }
  
  a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: color 0.3s ease;
    
    &:hover {
      color: #42b883;
    }
  }
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 32px;
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
  
  p {
    margin: 8px 0;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .nav-links {
    display: none;
  }
  
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .features-container {
    grid-template-columns: 1fr;
    gap: 32px;
  }
  
  .sponsor-logos {
    flex-direction: column;
    align-items: center;
  }
}

// 暗色主题
:global(.dark) {
  .features-section {
    background: #1a1a1a;
  }
  
  .feature-title {
    color: #e2e8f0;
  }
  
  .feature-description {
    color: #a0a0a0;
  }
  
  .sponsors-section {
    background: #2d2d2d;
  }
  
  .sponsor-title {
    color: #a0a0a0;
  }
  
  .logo-placeholder {
    background: #404040 !important;
    color: #a0a0a0 !important;
  }
}
</style>
