import axios from 'axios'
import type { AxiosRequestConfig } from 'axios'

// 创建一个axios实例
const service = axios.create({
    baseURL: '/api', // 设置请求的基础URL
    timeout: 1000, // 设置请求超时时间
    headers: {
        'Content-Type': 'application/json' // 设置请求头
    }
})

// 请求拦截器
service.interceptors.request.use(
    config => {
         // 从本地存储中读取token
        const token = localStorage.getItem('token')
        // 如果存在token，则将token添加到请求头中
        if (token) {// 如果token存在（用户已登录）
            config.headers.Authorization = `Bearer ${token}` // 在请求头中添加token
        }
        return config
    },
    error => {
        console.log(error)
        return Promise.reject(error)
    }
    
)

// 响应拦截器
service.interceptors.response.use(
    response => {
        // 直接返回数据，不做额外处理，由调用方决定如何处理不同code
        if(response.data.code !== 200){
            return Promise.reject(response.data.message)
        }
        return response.data
    },
    error => {
        console.log('请求错误', error)
        return Promise.reject(error)
    }
)

// 封装一个泛型请求方法
const request = <T = any>(config: AxiosRequestConfig): Promise<T> => {
    return service(config) as unknown as Promise<T>
}

export default request