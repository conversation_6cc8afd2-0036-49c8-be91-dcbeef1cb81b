# Vue.js 主页面模板

这是一个基于 Vue 3 + TypeScript + Vite 的现代化主页面模板，模仿了 Vue.js 官网的设计风格。

## ✨ 特性

- 🎨 **现代化设计** - 参考 Vue.js 官网的设计风格
- 🌙 **暗色主题** - 支持亮色/暗色主题切换
- 📱 **响应式布局** - 完美适配桌面端和移动端
- ⚡ **流畅动画** - 丰富的交互动画效果
- 🧭 **智能导航** - 响应式导航栏，支持平滑滚动
- 🎯 **TypeScript** - 完整的类型支持
- 🔧 **现代工具链** - Vite + Vue 3 + SCSS

## 🚀 快速开始

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

## 📁 项目结构

```
src/
├── components/          # 组件目录
│   ├── Navigation.vue   # 导航栏组件
│   ├── login.vue       # 登录组件
│   └── register.vue    # 注册组件
├── views/              # 页面目录
│   └── home.vue        # 主页面
├── style/              # 样式目录
│   └── index.scss      # 全局样式
├── router/             # 路由配置
├── store/              # 状态管理
└── main.ts             # 入口文件
```

## 🎨 主要功能

### 主页面特性
- **Hero 区域** - 渐进式动画展示
- **特性展示** - 三栏特性介绍，支持悬停效果
- **赞助商区域** - 展示赞助商信息
- **响应式页脚** - 完整的页脚链接

### 导航功能
- **智能导航** - 自动高亮当前页面
- **主题切换** - 一键切换亮色/暗色主题
- **移动端菜单** - 汉堡菜单，适配移动设备
- **平滑滚动** - 锚点跳转支持平滑滚动

### 动画效果
- **入场动画** - 页面加载时的渐入效果
- **悬停动画** - 卡片和按钮的交互动画
- **主题切换** - 平滑的主题过渡效果

## 🛠️ 技术栈

- **Vue 3** - 渐进式 JavaScript 框架
- **TypeScript** - 类型安全的 JavaScript
- **Vite** - 下一代前端构建工具
- **Vue Router** - 官方路由管理器
- **Pinia** - 状态管理库
- **SCSS** - CSS 预处理器
- **Element Plus** - Vue 3 组件库

## 📱 响应式设计

- **桌面端** (>768px) - 完整的多栏布局
- **移动端** (≤768px) - 单栏布局，汉堡菜单

## 🎯 浏览器支持

- Chrome >= 87
- Firefox >= 78
- Safari >= 14
- Edge >= 88

## 📄 许可证

MIT License
