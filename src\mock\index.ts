import Mock from 'mockjs'

interface LoginRequestBody {
  username: string;
  password: string;
}

// 登录接口Mock
Mock.mock('/api/user/login', 'post', (options: any) => {
  const { username, password } = JSON.parse(options.body) as LoginRequestBody

  // 模拟判断：如果用户名是admin、密码123456，就返回"登录成功"
  if (username === 'admin' && password === '123456') {
    return {
      code: 200,
      message: '登录成功',
      data: {
        token: 'fake_token_' + Date.now(), // 添加时间戳使每次token不同
        userInfo: {
          id: 1,
          username: 'admin',
          nickname: '管理员',
          avatar: 'https://avatars.githubusercontent.com/u/1?v=4'
        }
      }
    }
  } else {
    return {
      code: 401,
      message: '用户名或密码错误'
    }
  }
})

// 注册接口Mock
Mock.mock('/api/user/register', 'post', (options: any) => {
  const { username, password, email } = JSON.parse(options.body)

  // 简单模拟注册逻辑
  if (username && password) {
    return {
      code: 200,
      message: '注册成功',
      data: {
        id: Math.floor(Math.random() * 1000),
        username,
        email
      }
    }
  } else {
    return {
      code: 400,
      message: '用户名和密码不能为空'
    }
  }
})

// 获取用户信息接口Mock
Mock.mock('/api/user/info', 'get', () => {
  return {
    code: 200,
    message: '获取成功',
    data: {
      id: 1,
      username: 'admin',
      nickname: '管理员',
      email: '<EMAIL>',
      avatar: 'https://avatars.githubusercontent.com/u/1?v=4',
      role: 'admin'
    }
  }
})

console.log('Mock.js 已启动，模拟接口已配置')