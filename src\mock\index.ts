import Mock from 'mockjs'

interface LoginRequestBody {
  username: string;
  password: string;
}

Mock.mock(
  '/api/user/login',
   'post',
    (response: {body: string}) => {
const { username, password } = JSON.parse(response.body) as LoginRequestBody
  //获取用户信息
  
  // 模拟判断：如果用户名是admin、密码123456，就返回"登录成功"
  if (username === 'admin' && password === '123456') {
    return {
      code: 200,
      message: '登录成功',
      data: { token: 'fake_token_' + Date.now() } // 添加时间戳使每次token不同
    }
  } else {
    return { 
      code: 401, 
      message: '用户名或密码错误' 
    }
  }
})