import { defineStore } from 'pinia'
import {  type LoginParams, type LoginResponse } from '@/api/type'
import { ElMessage } from 'element-plus'
import { userlogin } from '@/api/user'
// 定义用户状态存储
export const useUserStore = defineStore('user', {
  // 状态
  state: () => ({
    token: localStorage.getItem('token') || '',
    userInfo: {} as Record<string, any>,
  }),
  
  // 计算属性
  getters: {
    isLoggedIn: (state) => !!state.token,
  },
  
  // 方法
  actions: {
    // 登录方法
    async login(loginForm:LoginParams) {
      try{
        if(!loginForm.username.trim()){
          console.error('请输入用户名')
          return;
        }
        if(!loginForm.password.trim()){
          console.error('请输入密码')
          return;
        }
        const res:LoginResponse = await userlogin(loginForm)
        if(res.code===200){
          this.token = res.data?.token || ''
          localStorage.setItem('token', this.token)// 用户登录成功后，前端会将服务器返回的token存入localStorage
          ElMessage({
            message: '登录成功',
            type: 'success'
          })
          return true
        }
      }
      catch(error){
        console.log('登录出错:', error)
        ElMessage({
          message: '登录失败，请稍后再试',
          type: 'error'
        })
        return false
      }
    },
    // 登出方法
    logout() {
      this.token = ''
      this.userInfo = {}
      localStorage.removeItem('token')
    },
  },
})
// 1. 用户提交登录表单
// 2. 调用login()异步函数
// 3. 执行到await时暂停
// 4. 网络请求发送到服务器
// 5. 服务器验证用户名密码
// 6. 服务器返回包含token的JSON数据
// 7. await解析Promise，将结果赋值给response变量
// 8. 继续执行后续的token存储逻辑
// 使用 await 后，代码更易读和维护：
// try {
//   // 等待API响应
//   const response = await userlogin(loginForm);
//   // 直接处理响应数据
// } catch (error) {
//   // 统一处理异常
// }