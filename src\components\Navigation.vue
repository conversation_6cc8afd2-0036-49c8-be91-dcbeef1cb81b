<template>
  <nav class="navigation">
    <div class="nav-container">
      <router-link to="/home" class="nav-brand">
        <img src="/favicon.ico" alt="Vue.js" class="logo">
        <span class="brand-text">Vue.js</span>
      </router-link>
      
      <div class="nav-menu" :class="{ 'nav-menu-open': isMenuOpen }">
        <router-link to="/home" class="nav-link" @click="closeMenu">首页</router-link>
        <a href="#features" class="nav-link" @click="closeMenu">特性</a>
        <a href="#sponsors" class="nav-link" @click="closeMenu">赞助商</a>
        <router-link to="/login" class="nav-link" @click="closeMenu">登录</router-link>
        <router-link to="/register" class="nav-link" @click="closeMenu">注册</router-link>
      </div>
      
      <div class="nav-actions">
        <button class="theme-toggle" @click="toggleTheme" :title="isDark ? '切换到亮色主题' : '切换到暗色主题'">
          {{ isDark ? '☀️' : '🌙' }}
        </button>
        <button class="mobile-menu-toggle" @click="toggleMenu" :class="{ active: isMenuOpen }">
          <span></span>
          <span></span>
          <span></span>
        </button>
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 主题切换
const isDark = ref(false)
const isMenuOpen = ref(false)

const toggleTheme = () => {
  isDark.value = !isDark.value
  document.documentElement.classList.toggle('dark', isDark.value)
  localStorage.setItem('theme', isDark.value ? 'dark' : 'light')
}

const toggleMenu = () => {
  isMenuOpen.value = !isMenuOpen.value
}

const closeMenu = () => {
  isMenuOpen.value = false
}

onMounted(() => {
  // 检查本地存储的主题偏好
  const savedTheme = localStorage.getItem('theme')
  if (savedTheme) {
    isDark.value = savedTheme === 'dark'
  } else {
    // 检查系统主题偏好
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
    isDark.value = prefersDark
  }
  document.documentElement.classList.toggle('dark', isDark.value)
})
</script>

<style scoped lang="scss">
.navigation {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: 12px;
  text-decoration: none;
  
  .logo {
    width: 32px;
    height: 32px;
  }
  
  .brand-text {
    font-size: 20px;
    font-weight: 600;
    color: #42b883;
  }
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: 32px;
  
  @media (max-width: 768px) {
    position: fixed;
    top: 64px;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    flex-direction: column;
    padding: 24px;
    gap: 16px;
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    
    &.nav-menu-open {
      transform: translateY(0);
      opacity: 1;
      visibility: visible;
    }
  }
}

.nav-link {
  color: #2c3e50;
  text-decoration: none;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
  
  &:hover {
    color: #42b883;
    background-color: rgba(66, 184, 131, 0.1);
  }
  
  &.router-link-active {
    color: #42b883;
    background-color: rgba(66, 184, 131, 0.15);
  }
}

.nav-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.theme-toggle {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.1);
    transform: scale(1.1);
  }
}

.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  gap: 4px;
  
  span {
    width: 20px;
    height: 2px;
    background: #2c3e50;
    transition: all 0.3s ease;
    border-radius: 1px;
  }
  
  &.active {
    span:nth-child(1) {
      transform: rotate(45deg) translate(5px, 5px);
    }
    
    span:nth-child(2) {
      opacity: 0;
    }
    
    span:nth-child(3) {
      transform: rotate(-45deg) translate(7px, -6px);
    }
  }
  
  @media (max-width: 768px) {
    display: flex;
  }
}

// 暗色主题
:global(.dark) {
  .navigation {
    background: rgba(30, 30, 30, 0.95);
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }
  
  .nav-link {
    color: #e2e8f0;
    
    &:hover {
      color: #42b883;
      background-color: rgba(66, 184, 131, 0.2);
    }
    
    &.router-link-active {
      color: #42b883;
      background-color: rgba(66, 184, 131, 0.25);
    }
  }
  
  .theme-toggle {
    color: #e2e8f0;
    
    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }
  
  .mobile-menu-toggle span {
    background: #e2e8f0;
  }
  
  .nav-menu {
    @media (max-width: 768px) {
      background: rgba(30, 30, 30, 0.98);
    }
  }
}
</style>
