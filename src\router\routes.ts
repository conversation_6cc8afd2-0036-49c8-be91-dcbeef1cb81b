export const constantRouter=[
      {
        path: '/',
        redirect: '/login'
      },
      {
        path: '/login',
        name: 'Login',
        component: () => import('@/views/login.vue')
      },
      {
        path: '/register',
        name: 'Register',
        component: () => import('@/views/register.vue')
      }
      ,
      {
        path : '/home',
        name : 'Home',
        component : () => import('@/views/home.vue')
      }
]