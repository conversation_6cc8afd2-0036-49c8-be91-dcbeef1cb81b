export const constantRouter=[
      {
        path: '/',
        redirect: '/home'
      },
      {
        path: '/login',
        name: 'Login',
        component: () => import('@/components/login.vue')
      },
      {
        path: '/register',
        name: 'Register',
        component: () => import('@/components/register.vue')
      }
      ,
      {
        path : '/home',
        name : 'Home',
        component : () => import('@/views/home.vue')
      }
]